// 
import { is<PERSON><PERSON><PERSON> } from "./Utils";

// AC自动机算法
class AhoCorasick {
    goto: any;
    output: any;
    fail: any;
    constructor(keywords: any) {
      this.goto = {};
      this.output = {};
      this.fail = {};
      this.buildGoto(keywords);
      this.buildFailure();
    }

    buildGoto(keywords: any) {
      let newState = 0; // 状态编号 从 0 开始

      for (let keyword of keywords) {
        let currentState = 0;

        for (let char of keyword) {
          if (!this.goto[currentState]) {
          // 如果当前状态没有东西就初始化一个空对象
            this.goto[currentState] = {};
          }
          if (!this.goto[currentState][char]) {
          // 如果当前状态下的对象里边 没有当前字符的话 就用下一个状态编号
            this.goto[currentState][char] = ++newState;
          }
          currentState = this.goto[currentState][char]; // 更新当前状态的值
        }

        if (!this.output[currentState]) {
          this.output[currentState] = [];
        }
        this.output[currentState].push(keyword);
      }
    }

    buildFailure() {
      let queue = [];
      for (let key in this.goto[0]) {
        let state = this.goto[0][key];
        this.fail[state] = 0;
        queue.push(state);
      }

      while (queue.length > 0) {
        let state: any = queue.shift();

        for (let key in this.goto[state]) {
          let failState = this.fail[state];
          let transitionState = this.goto[state][key];

          while (failState !== 0 && !this.goto[failState]?.[key]) {
            failState = this.fail[failState];
          }
          if (this.goto[failState] && this.goto[failState][key]) {
            this.fail[transitionState] = this.goto[failState][key];
          } else {
            this.fail[transitionState] = 0;
          }

          if (!this.output[transitionState]) {
            this.output[transitionState] = [];
          }
          this.output[transitionState] = this.output[transitionState].concat(
            this.output[this.fail[transitionState]] || []
          );

          queue.push(transitionState);
        }
      }
    }

    search(paragraph: any) {
      let currentState = 0;
      let results = [];
      let charIndices = []; // 记录字符的真实索引

      for (let i = 0; i < paragraph.length; i++) {
        if (isCharacter(paragraph[i]) &&  paragraph[i].value && paragraph[i].field_position === "normal") {
          charIndices.push(i);
          let char = paragraph[i].value;

          while (currentState !== 0 && (!this.goto[currentState] || !this.goto[currentState][char])) {
            currentState = this.fail[currentState];
          }

          if (this.goto[currentState] && this.goto[currentState][char]) {
            currentState = this.goto[currentState][char];
          }

          if (this.output[currentState]) {
            for (let keyword of this.output[currentState]) {
              results.push({
                keyword: keyword,
                start: charIndices[charIndices.length - keyword.length],
                end: charIndices[charIndices.length - 1]
              });
            }
          }
        }
      }

      return results;
    }
}

// 增强版AC自动机，专门用于transformTextToField功能
export class EnhancedAhoCorasick {
    goto: any;      // goto函数：状态转移表，记录从一个状态通过某个字符转移到下一个状态
    output: any;    // output函数：输出表，记录每个状态对应的匹配关键词
    fail: any;      // fail函数：失败函数，当匹配失败时跳转到的状态

    constructor(keywords: string[]) {
      this.goto = {};     // 初始化状态转移表为空对象
      this.output = {};   // 初始化输出表为空对象
      this.fail = {};     // 初始化失败函数表为空对象
      this.buildGoto(keywords);    // 构建goto函数和output函数
      this.buildFailure();         // 构建fail函数
    }

    buildGoto(keywords: string[]) {
      let newState = 0; // 状态编号计数器，从0开始，每次分配新状态时递增

      // 遍历每个关键词，为其在Trie树中构建路径
      for (let keyword of keywords) {
        let currentState = 0; // 当前状态，总是从初始状态0开始

        // 遍历关键词的每个字符，逐步构建状态转移路径
        for (let char of keyword) {
          // 如果当前状态在goto表中不存在，就初始化为空对象
          // 这个对象将存储从当前状态出发的所有字符转移
          if (!this.goto[currentState]) {
            this.goto[currentState] = {};
          }

          // 如果从当前状态通过当前字符的转移不存在
          // 就创建一个新状态，状态编号为++newState
          if (!this.goto[currentState][char]) {
            this.goto[currentState][char] = ++newState;
          }

          // 更新当前状态为转移后的状态，继续处理下一个字符
          currentState = this.goto[currentState][char];
        }

        // 关键词处理完毕，currentState就是这个关键词的终止状态
        // 初始化该状态的输出数组（如果不存在）
        if (!this.output[currentState]) {
          this.output[currentState] = [];
        }
        // 将当前关键词添加到终止状态的输出中
        this.output[currentState].push(keyword);
      }
    }

    // fail函数的计算只依赖当前路径的后缀和Trie 树中已存在的路径，与节点是否有后续转移无关
    buildFailure() {
      let queue = []; // BFS队列，用于广度优先遍历所有状态

      // 初始化：处理深度为1的状态（从初始状态0直接可达的状态）
      for (let key in this.goto[0]) {        // 遍历从状态0出发的所有字符转移
        let state = this.goto[0][key];        // 获取转移到的状态
        this.fail[state] = 0;                 // 深度为1的状态的失败函数都指向初始状态0
        queue.push(state);                    // 将该状态加入队列，准备处理其子状态
      }

      // BFS遍历：处理深度大于1的状态
      while (queue.length > 0) {
        let state: any = queue.shift();      // 从队列头部取出一个状态进行处理

        // 遍历当前状态的所有出边（字符转移）
        for (let key in this.goto[state]) {
          let failState = this.fail[state];           // 获取当前状态的失败状态
          let transitionState = this.goto[state][key]; // 获取通过字符key转移到的状态

          // 寻找合适的失败状态：
          // 从当前状态的失败状态开始，向上查找能够通过字符key转移的状态
          while (failState !== 0 && !this.goto[failState]?.[key]) {
            failState = this.fail[failState];  // 如果当前失败状态无法通过key转移，继续向上查找
          }

          // 设置转移状态的失败函数
          if (this.goto[failState] && this.goto[failState][key]) {
            // 如果找到了能通过key转移的状态，失败函数指向该转移结果
            this.fail[transitionState] = this.goto[failState][key];
          } else {
            // 否则失败函数指向初始状态0
            this.fail[transitionState] = 0;
          }

          // 合并输出：将失败状态的输出合并到当前状态
          if (!this.output[transitionState]) {
            this.output[transitionState] = [];  // 初始化输出数组
          }
          // 将失败状态的所有输出添加到当前状态的输出中
          // 这样可以识别重叠的模式匹配
          this.output[transitionState] = this.output[transitionState].concat(
            this.output[this.fail[transitionState]] || []
          );

          queue.push(transitionState);  // 将转移状态加入队列，继续处理其子状态
        }
      }
    }

    /**
     * 在段落中搜索关键词并返回详细位置信息
     * @param paragraph 段落对象，包含characters数组
     * @param paragraphIndex 段落在文档中的索引
     * @returns 匹配结果数组，包含关键词、段落索引、字符索引等信息
     */
    searchWithPosition(paragraph: any, paragraphIndex: number): any[] {
      let currentState = 0;        // AC自动机的当前状态，从初始状态0开始
      let results: any[] = [];     // 存储所有匹配结果的数组
      let charIndices: number[] = []; // 记录有效字符在原始characters数组中的真实索引
      let charValues: string[] = [];  // 记录有效字符的值，用于构建连续的搜索文本

      try {
        // 安全检查：确保段落对象有效且包含characters数组
        if (!paragraph || !paragraph.characters || !Array.isArray(paragraph.characters)) {
          return results;  // 如果段落无效，返回空结果
        }

        // 第一步：提取有效字符
        // 遍历段落中的所有元素，只提取普通文本字符（排除文本域、图片等特殊元素）
        for (let i = 0; i < paragraph.characters.length; i++) {
          const char = paragraph.characters[i];  // 获取当前字符对象

          // 详细的字符有效性检查
          if (char && typeof char === 'object') {  // 确保字符对象存在且为对象类型
            // 检查是否为普通字符且不在文本域内
            if (isCharacter(char) && char.value && char.field_position === "normal") {
              charIndices.push(i);        // 记录该字符在原数组中的索引
              charValues.push(char.value); // 记录该字符的值
            }
          }
        }

      } catch (error) {
        return results;  // 如果处理过程中出错，返回空结果
      }

      // 第二步：AC自动机匹配
      // 在提取的连续字符序列中进行模式匹配
      for (let i = 0; i < charValues.length; i++) {
        let char = charValues[i];  // 获取当前要匹配的字符

        // 状态转移：如果当前状态无法通过当前字符转移，使用失败函数回退
        while (currentState !== 0 && (!this.goto[currentState] || !this.goto[currentState][char])) {
          currentState = this.fail[currentState];  // 回退到失败状态
        }

        // 如果找到了有效的状态转移，执行转移
        if (this.goto[currentState] && this.goto[currentState][char]) {
          currentState = this.goto[currentState][char];
        }

        // 检查当前状态是否有输出（即是否匹配到了关键词）
        if (this.output[currentState]) {
          // 遍历当前状态的所有输出关键词
          for (let keyword of this.output[currentState]) {
            const matchLength = keyword.length;                    // 匹配关键词的长度
            const startCharIndex = charIndices[i - matchLength + 1]; // 匹配开始位置在原数组中的索引
            const endCharIndex = charIndices[i];                   // 匹配结束位置在原数组中的索引

            // 创建匹配结果对象
            results.push({
              keyword: keyword,              // 匹配到的关键词
              paragraphIndex: paragraphIndex, // 段落索引
              startCharIndex: startCharIndex, // 在段落characters数组中的起始索引
              endCharIndex: endCharIndex,     // 在段落characters数组中的结束索引
              matchLength: matchLength        // 匹配长度
            });
          }
        }
      }

      return results;  // 返回所有匹配结果
    }
}

export default function findKeywordsInParagraphs(keywords: any, paragraphs: any) {
  let ac = new AhoCorasick(keywords);
  let results = [];

  for (let i = 0; i < paragraphs.length; i++) {
    let matches = ac.search(paragraphs[i]);
    for (let match of matches) {
      results.push({
        paragraphIndex: i,
        keyword: match.keyword,
        start: match.start,
        end: match.end
      });
    }
  }

  return results;
}